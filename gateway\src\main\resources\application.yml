server:
  port: 8080  # 服务端口
spring:
  application:
    name: gateway # 服务名
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
    gateway:
      discovery:
        locator:
          enabled: true  # Gateway开启服务注册和发现功能
          lower-case-service-id: true  # 将请求路径上的服务名配置为小写
      routes:
        - id: guestbook                     #网关路由
          uri: lb://guestbook
          predicates:
            - Path=/guestbook/**
        - id: user-service                     #网关路由
          uri: lb://userservice
          predicates:
            - Path=/userservice/**
        - id: clustercalculate                     #网关路由
          uri: lb://clustercalculate
          predicates:
            - Path=/clustercalculate/**
        - id: pathcalculate                     #网关路由
          uri: lb://pathcalculate
          predicates:
            - Path=/pathcalculate/**
        - id: datamanagement                     #网关路由
          uri: lb://datamanagement
          predicates:
            - Path=/datamanagement/**
        - id: pickup                     #网关路由
          uri: lb://pickup
          predicates:
            - Path=/pickup/**
        - id: static-files               #静态文件路由
          uri: file://${file.savePath}/
          predicates:
            - Path=/${file.accessPathPrefix}/**
          filters:
            - RewritePath=/${file.accessPathPrefix}/(?<segment>.*), /$\{segment}
      globalcors: # 全局的跨域处理
        add-to-simple-url-handler-mapping: true # 解决options请求被拦截问题
        corsConfigurations:
          '[/**]':
            allowedOrigins: # 允许哪些网站的跨域请求
              - "*"  # 待补充！！！
            allowedMethods: # 允许的跨域ajax的请求方式
              - "GET"
              - "POST"
              - "DELETE"
              - "PUT"
              - "OPTIONS"
              - "PATCH"
            allowedHeaders: "*" # 允许在请求中携带的头信息
            allowCredentials: true # 是否允许携带cookie
            maxAge: 360000 # 这次跨域检测的有效期
file:
  accessPathPrefix: file
  savePath: E:\www\wwwroot\ycwl\resource\file