package com.ict.datamanagement.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.auth0.jwt.JWT;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.datamanagement.domain.dto.car.*;
import com.ict.datamanagement.domain.dto.car.actual.AddCarActualRequest;
import com.ict.datamanagement.domain.dto.car.actual.CarActualListRequest;
import com.ict.datamanagement.domain.dto.car.actual.UpdateCarActualRequest;
import com.ict.datamanagement.domain.entity.*;
import com.ict.datamanagement.domain.vo.carVO.AddCarDownBoxVO;
import com.ict.datamanagement.domain.vo.carVO.CarActualDownBoxVO;
import com.ict.datamanagement.domain.vo.carVO.CarActualVO;
import com.ict.datamanagement.domain.vo.carVO.CarVO;
import com.ict.datamanagement.mapper.*;
import com.ict.datamanagement.service.CarService;
import com.ict.datamanagement.util.CsvImportUtil;
import com.ict.datamanagement.util.CsvUtils;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.Internal;
import org.apache.poi.util.StringUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service
public class CarServiceImpl extends ServiceImpl<CarMapper, Car> implements CarService {

    @Autowired
    private CarMapper carMapper;

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private CarDriverMapper carDriverMapper;

    @Autowired
    private DeliveryMapper deliveryMapper;

    @Autowired
    private FileImportLogsMapper fileImportLogsMapper;

    @Autowired
    private CsvImportUtil csvImportUtil;


    @Value("${file.DOWNLOAD_PATH}")
    private String DOWNLOAD_PATH;


    @Override
    public List<CarVO> getCarList(CarListRequest carListRequest) {
        //处理车牌号
        String licensePlateNumber = carListRequest.getLicensePlateNumber();
        //处理班组
        String teamName = carListRequest.getTeamName();
        //处理驾驶人
        String carDriver = carListRequest.getCarDriver();
        //处理状态
        String status = carListRequest.getStatus();
        if ("正常".equals(status)) {
            status = "1";
        } else if ("异常".equals(status)) {
            status = "0";
        }
        //处理最大载重
        String maxLoad = carListRequest.getMaxLoad();
        //查询数据库
        List<CarVO> cars = carMapper.mySelectCarList(licensePlateNumber, teamName, carDriver, status, maxLoad);
        List<CarVO> carVOS = new ArrayList<>();
        for (CarVO car : cars) {
            /*Long carId = car.getCarId();
            Car car1 = carMapper.selectById(carId);
            Long transitDepotId = car1.getTransitDepotId();
            String teamName1 = transitDepotMapper.selectGroupById(transitDepotId);
            car.setTeamName(teamName1);*/
            String status1 = car.getStatus();
            if (status1.equals("1")) {
                car.setStatus("正常");
            } else {
                car.setStatus("异常");
            }
            carVOS.add(car);
        }
        return carVOS;
    }

    @Override
    public Page<CarVO> getPage(Integer pageNum, Integer pageSize, List<CarVO> carVOS) {
        Page<CarVO> page = new Page<>(pageNum, pageSize);
        long fromIndex = (long) (pageNum - 1) * pageSize;
        long toIndex = Math.min(fromIndex + pageSize, carVOS.size());
        List<CarVO> resList = new ArrayList<CarVO>();
        if (fromIndex < carVOS.size()) {
            resList = carVOS.subList((int) fromIndex, (int) toIndex);
        }
        page.setRecords(resList);
        page.setTotal(carVOS.size());
        return page;
    }

    @Override
    public CarDownBox selectDownBox() {
        CarDownBox carDownBox = new CarDownBox();
        //处理车牌号
        List<String> lpns = carMapper.selectLicensePlateNumber();
        carDownBox.setLicensePlateNumberList(lpns);
        //处理班组
        List<String> teamNames = teamMapper.selectNames();
        carDownBox.setTeamList(teamNames);
        //处理驾驶人
        HashMap<String, String> hashMap = new HashMap<>();
        List<CarDriver> carDrivers = carDriverMapper.selectList(new QueryWrapper<CarDriver>().eq("position","物流部经办人员"));
        for (CarDriver carDriver : carDrivers) {
            hashMap.put(carDriver.getUserName(), carDriver.getPhone());
        }
        carDownBox.setCarDriverList(hashMap);
        //处理状态
        ArrayList<String> status = new ArrayList<>();
        status.add("正常");
        status.add("异常");
        carDownBox.setStatusList(status);
        //处理最大载重量
        List<String> maxLoads = carMapper.selectMaxLoad();
        carDownBox.setMaxLoad(maxLoads);

        return carDownBox;
    }

    @Override
    public String deleteCar(Long carId) {
        //校验该车辆是否被车辆实情依赖
        Long aLong = carMapper.selectCount(new QueryWrapper<Car>().eq("is_fact", "1").eq("car_id", carId));
        if (aLong > 0) {
            return "删除失败，该车辆下存在工作实情";
        }
        int i1 = carMapper.deleteById(carId);
        if (i1 == 0) {
            return "删除失败";
        }
        return "删除成功";
    }

    @Override
    public String addCar(AddCarRequest addCarRequest) {
        //创建car对象
        Car car = new Car();
        //填充车牌号
        //判断车牌号是否有重复
        int i = carMapper.checkLicensePlateNumber(addCarRequest.getLicensePlateNumber());
        if (i > 0) {
            return "车牌号重复";
        }

        car.setLicensePlateNumber(addCarRequest.getLicensePlateNumber());
        //创建联系人对象
        CarDriver carDriver = new CarDriver();
        //填充驾驶人
        String carDriverName = addCarRequest.getCarDriverName();
        carDriver.setUserName(carDriverName);
        CarDriver carDriver1 = carDriverMapper.selectByName(carDriverName);
        car.setCarDriverId(carDriver1.getUserId());

        //填充最大载重量
        car.setMaxLoad(addCarRequest.getMaxLoad());
        //状态
        car.setStatus(addCarRequest.getStatus());
        if (StringUtil.isNotBlank(addCarRequest.getStatus())) {
            if ("正常".equals(addCarRequest.getStatus())) {
                car.setStatus("1");
            } else {
                car.setStatus("0");
            }
        }
        //填充配送域
        String deliveryAreaName = addCarRequest.getDeliveryAreaName();
        Delivery delivery = deliveryMapper.selectByName(deliveryAreaName);
        if (delivery != null) {
            int deliveryAreaId = delivery.getDeliveryAreaId();
            car.setDeliveryAreaId(deliveryAreaId);
            car.setTransitDepotId((long) delivery.getTransitDepotId());
            //将对应配送域下的车辆数加1
            //判断添加的车辆的状态，如果是异常状态则不添加车辆
            if("1".equals(car.getStatus())) {
                delivery.setCarNumber(delivery.getCarNumber() + 1);
                //更新数据库
                deliveryMapper.updateById(delivery);
                //在对应班组下添加车辆数
                Team team = teamMapper.selectById(delivery.getTeamId());
                team.setCarSum(team.getCarSum()+1);
                team.setRouteSum(team.getCarSum()*5);
                teamMapper.updateTeamById(team);
            }

        } else {
            return "配送域不存在";
        }

        //设置该车辆为基本信息
        car.setIs_fact("0");
        //插入数据库
        int insert1 = carMapper.insert(car);
        if (insert1 == 1) {
            return "添加成功";
        }
        return "添加失败";
    }

    @Override
    public int updateCar(UpdateCarRequest updateCarRequest) {
        Long carId = updateCarRequest.getCarId();
        Car car = carMapper.selectById(carId);
        //处理驾驶人
        String carDriverName = updateCarRequest.getCarDriverName();
        if (StringUtil.isNotBlank(carDriverName)) {
            CarDriver carDriver = carDriverMapper.selectByName(carDriverName);
            car.setCarDriverId(carDriver.getUserId());
        }
        //最大载重量
        String maxLoad = updateCarRequest.getMaxLoad();
        if (StringUtil.isNotBlank(maxLoad)) {
            car.setMaxLoad(maxLoad);
        }
        //记录原始车辆状态
        String yStatus=car.getStatus();
        //状态
        String status = updateCarRequest.getStatus();
        if (StringUtil.isNotBlank(status)) {
            if (status.equals("正常")) {
                status = "1";
            } else {
                status = "0";
            }
            car.setStatus(status);
        }
        //记录原始配送域id
        int deliveryId=car.getDeliveryAreaId();
        //处理配送域id
        Delivery delivery1 = deliveryMapper.selectByName(updateCarRequest.getDeliveryAreaName());
        car.setTransitDepotId((long) delivery1.getTransitDepotId());
        car.setDeliveryAreaId(delivery1.getDeliveryAreaId());
        //更新数据库
        int i = carMapper.myUpdateById(car);
        if(i==1){
            //修改配送域
            //1.如果和原来配送域一样则无需处理
            Delivery delivery = deliveryMapper.selectByName(updateCarRequest.getDeliveryAreaName());//目标配送域
            if(deliveryId!=delivery.getDeliveryAreaId()){
                int count=0;
                if("1".equals(status)){
                    count=1;
                }
                //更新目标配送域的车辆数
                Long carNum = carMapper.selectCount(new QueryWrapper<Car>().eq("is_delete", 0).eq("status",1).eq("is_fact", 0).eq("delivery_area_id", delivery.getDeliveryAreaId()));
                delivery.setCarNumber(Math.toIntExact(carNum));
                //更新车辆数
                delivery.setRouteNumber((int) ((carNum)*5));
                //更新目标班组的车辆数
                Team team = teamMapper.selectById(delivery.getTeamId());
                team.setCarSum(team.getCarSum()+count);
                team.setRouteSum(team.getCarSum()*5);
                deliveryMapper.updateById(delivery);
                teamMapper.updateTeamById(team);

                //处理原始配送域和班组的车辆数
                Delivery delivery2 = deliveryMapper.selectById(deliveryId);
                Long carNum2 = carMapper.selectCount(new QueryWrapper<Car>().eq("is_delete", 0).eq("status",1).eq("is_fact", 0).eq("delivery_area_id", deliveryId));
                delivery2.setCarNumber(Math.toIntExact(carNum2));
                delivery2.setRouteNumber((int) (carNum2*5));
                deliveryMapper.updateById(delivery2);

                Team team2 = teamMapper.selectById(delivery2.getTeamId());
                team2.setCarSum(team2.getCarSum()-count);
                System.out.println(team2.getCarSum());
                team2.setRouteSum(team2.getCarSum()*5);
                teamMapper.updateTeamById(team2);
            }else{
                //处理原始配送域和原始班组
                //更新目标配送域的车辆数
                Delivery delivery2 = deliveryMapper.selectById(deliveryId);
                Long carNum = carMapper.selectCount(new QueryWrapper<Car>().eq("is_delete", 0).eq("status",1).eq("is_fact", 0).eq("delivery_area_id", deliveryId));
                delivery2.setCarNumber(Math.toIntExact(carNum));
                delivery2.setRouteNumber((int) (carNum*5));
                //更新原始班组的车辆数
                Team team = teamMapper.selectById(delivery2.getTeamId());
                int count=0;
                if(!yStatus.equals(status)){
                    if("1".equals(yStatus)&&"0".equals(status)){
                        count=-1;
                    }
                    if("0".equals(yStatus)&&"1".equals(status)){
                        count=1;
                    }
                }
                team.setCarSum(team.getCarSum()+count);
                deliveryMapper.updateById(delivery2);
                teamMapper.updateTeamById(team);
            }
        }
        return i;
    }

    @Override
    public List<CarActualVO> getCarActualVOList(CarActualListRequest carActualListRequest) {
        //车牌号
        String licensePlateNumber = carActualListRequest.getLicensePlateNumber();
        //班组
        String teamName = carActualListRequest.getTeamName();
        //驾驶人
        String carDriverName = carActualListRequest.getCarDriverName();
        //时间
        String date = carActualListRequest.getDate();
        //查询数据库
        List<CarActualVO> carActualVOS = carMapper.getCarActualVOList(licensePlateNumber, teamName, carDriverName, date);
        return carActualVOS;
    }

    @Override
    public Page<CarActualVO> getActualPage(Integer pageNum, Integer pageSize, List<CarActualVO> carActualVOS) {
        Page<CarActualVO> page = new Page<>(pageNum, pageSize);
        long fromIndex = (long) (pageNum - 1) * pageSize;
        long toIndex = Math.min(fromIndex + pageSize, carActualVOS.size());
        List<CarActualVO> resList = new ArrayList<>();
        if (fromIndex < carActualVOS.size()) {
            resList = carActualVOS.subList((int) fromIndex, (int) toIndex);
        }
        page.setRecords(resList);
        page.setTotal(carActualVOS.size());
        return page;
    }

    @Override
    @Transactional
    public String addCarActual(AddCarActualRequest addCarActualRequest) {
        Car car = new Car();
        String licensePlateNumber = addCarActualRequest.getLicensePlateNumber();
        String date = addCarActualRequest.getDate();
        String route = addCarActualRequest.getRoute();
        //处理车牌号+日期相同不能添加
        //处理车牌号+路线不能添加
        int count = carMapper.checkLicensePlateNumberAndDataAndRoute(licensePlateNumber, date, route);
        if (count > 0) {
            return "工作实情重复,添加失败";
        }
        car.setLicensePlateNumber(addCarActualRequest.getLicensePlateNumber());
        //处理配送域
        String deliveryAreaName = addCarActualRequest.getDeliveryAreaName();
        if (StringUtil.isNotBlank(deliveryAreaName)) {
            Delivery delivery = deliveryMapper.selectByName(deliveryAreaName);
            if (delivery == null) {
                return "配送域不存在";
            }
            car.setDeliveryAreaId(delivery.getDeliveryAreaId());
        }
        //处理驾驶人
        String carDriverName = addCarActualRequest.getCarDriverName();
        if (StringUtil.isNotBlank(carDriverName)) {
            CarDriver carDriver = carDriverMapper.selectByName(carDriverName);
            car.setCarDriverId(carDriver.getUserId());
        }
        //处理星期
        car.setWeek(addCarActualRequest.getWeek());
        //处理实际载货量
        if (StringUtil.isNotBlank(addCarActualRequest.getActualLoad())) {
            car.setActualLoad(addCarActualRequest.getActualLoad());
        }
        //处理日期
        car.setDate(addCarActualRequest.getDate());
        //处理实际工作时长
        if (StringUtil.isNotBlank(addCarActualRequest.getActualTime())) {
            car.setActualTime(addCarActualRequest.getActualTime());
        }
        //处理路线
        if (StringUtil.isNotBlank(addCarActualRequest.getRoute())) {
            car.setRouteName(addCarActualRequest.getRoute());
        }
        //设置类型
        car.setIs_fact("1");
        //插入数据库
        int i1 = carMapper.insert(car);
        if (i1 == 0) {
            return "添加失败";
        }
        return "添加成功";
    }

    @Override
    public CarActualDownBoxVO getCarActualDownBox() {
        CarActualDownBoxVO carActualDownBoxVO = new CarActualDownBoxVO();
        //处理车牌号
        List<String> strings = carMapper.selectLicensePlateNumber();
        carActualDownBoxVO.setLicensePlateNumberList(strings);
        //处理配送域
        List<String> strings1 = deliveryMapper.selectNameList();
        carActualDownBoxVO.setDeliveryNameList(strings1);
        //处理驾驶人
        List<String> strings2 = carDriverMapper.selectName();
        carActualDownBoxVO.setCarDriverList(strings2);
        //返回数据
        return carActualDownBoxVO;
    }

    @Override
    public int updateCarActual(UpdateCarActualRequest updateCarActualRequest) {
        //从数据库中获取当前id下的车辆
        Long carId = updateCarActualRequest.getCarId();
        Car car = carMapper.selectById(carId);
        /*//处理车牌号
        String licensePlateNumber = updateCarActualRequest.getLicensePlateNumber();
        if (StringUtil.isNotBlank(licensePlateNumber)) {
            car.setLicensePlateNumber(licensePlateNumber);
        }
        //处理配送域
        String deliveryAreaName = updateCarActualRequest.getDeliveryAreaName();
        if (StringUtil.isNotBlank(deliveryAreaName)) {
            Delivery delivery = deliveryMapper.selectByName(deliveryAreaName);
            car.setDeliveryAreaId(delivery.getDeliveryAreaId());
        }
        //处理驾驶人
        String carDriverName = updateCarActualRequest.getCarDriverName();
        if (StringUtil.isNotBlank(carDriverName)) {
            CarDriver carDriver = carDriverMapper.selectByName(carDriverName);
            car.setCarDriverId(carDriver.getUserId());
        }
        //处理星期
        String week = updateCarActualRequest.getWeek();
        if (StringUtil.isNotBlank(week)) {
            car.setWeek(week);
        }*/
        //处理实际载货量
        String actualLoad = updateCarActualRequest.getActualLoad();
        if (StringUtil.isNotBlank(actualLoad)) {
            car.setActualLoad(actualLoad);
        }
        /*//处理日期
        String date = updateCarActualRequest.getDate();
        if (StringUtil.isNotBlank(date)) {
            car.setDate(date);
        }*/
        //处理实际工作时长
        String actualTime = updateCarActualRequest.getActualTime();
        if (StringUtil.isNotBlank(actualTime)) {
            car.setActualTime(actualTime);
        }
        //处理路线
        String route = updateCarActualRequest.getRoute();
        if (StringUtil.isNotBlank(route)) {
            car.setRouteName(route);
        }
        //更新数据库数据库
        int i = carMapper.updateById(car);
        return i;
    }

    @Override
    @Transactional
    public String readCsv(File csvFile, String formattedDate, String authorization) {
        String code = "UTF-8";
        try {
            code = detectEncoding(csvFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 将文件内容解析，存入List容器，List<String>为每一行内容的集合，6为CSV文件每行的总列数
        List<List<String>> lists = csvImportUtil.readCSV(csvFile.getPath(), 8, code);
        List<List<String>> errorList = new ArrayList<>();
        List<List<String>> succeedList = new ArrayList<>();
        List<Car> cars = new ArrayList<>();//收集成功的对象
        List<CarFile> carFiles = new ArrayList<>();//收集成功的对象
        List<CarFile> allCarList = new ArrayList<>();//收集所有数据
        allCarList.add(new CarFile());
        ArrayList<String> weekList = new ArrayList<>();
        weekList.add("星期一");
        weekList.add("星期二");
        weekList.add("星期三");
        weekList.add("星期四");
        weekList.add("星期五");
        weekList.add("星期六");
        weekList.add("星期日");

        if (lists != null) {
            for (List<String> list : lists) {
                Car car = new Car();
                CarFile carFile = new CarFile();
                //标记该行数据是否有错
                boolean flag = false;
                String errorInfo = "";
                //校验车牌号
                if (StringUtil.isNotBlank(list.get(0))) {
                    car.setLicensePlateNumber(list.get(0));
                    carFile.setLicensePlateNumber(list.get(0));
                    //查询数据库是否存在该车牌号
                    Long aLong = carMapper.selectCount(new QueryWrapper<Car>().eq("license_plate_number", car.getLicensePlateNumber()).eq("is_fact", "0").eq("is_delete", 0));
                    if (aLong == 0) {
                        flag = true;
                        errorInfo += "车牌号不存在-";
                    }
                } else {
                    flag = true;
                    //list.add("失败");
                    //list.add("车牌号数据为空");
                    errorInfo += "车牌号数据为空-";
                }
                //校验驾驶人名称
                if (StringUtil.isNotBlank(list.get(1))) {
                    CarDriver carDriver = carDriverMapper.selectByName(list.get(1));
                    if (carDriver == null) {
                        flag = true;
                        //list.add("失败");
                        //list.add("数据库中不存在该驾驶人");
                        errorInfo += "数据库中不存在该驾驶人-";
                        //errorList.add(list);
                        //continue;
                    } else {
                        carFile.setCarDriverName(list.get(1));
                        car.setCarDriverId(carDriver.getUserId());
                    }
                } else {
                    flag = true;
                    errorInfo += "驾驶人数据为空-";
                }
                //校验实际载货量
                if (StringUtil.isNotBlank(list.get(2))) {
                    car.setActualLoad(list.get(2));
                    carFile.setActualLoad(list.get(2));
                } else {
                    flag = true;
                    errorInfo += "实际载货量数据为空-";
                }
                //校验实际工作时长
                if (StringUtil.isNotBlank(list.get(3))) {
                    car.setActualTime(list.get(3));
                    carFile.setActualTime(list.get(3));
                } else {
                    flag = true;
                    errorInfo += "实际工作时长数据为空-";
                }
                //校验路线
                if (StringUtil.isNotBlank(list.get(4))) {
                    car.setRouteName(list.get(4));
                    carFile.setRouteName(list.get(4));
                    //校验车牌号和路线组合
                    int i1 = carMapper.selectCountByLicensePlateNumberAndRoute(car.getLicensePlateNumber(), list.get(4));
                    if (i1 > 0) {
                        flag = true;
                        errorInfo += "数据库中已存在车牌号和路线组合的数据-";
                    }
                } else {
                    flag = true;
                    errorInfo += "路线数据为空-";
                }
                //校验星期
                if (StringUtil.isNotBlank(list.get(5)) && weekList.contains(list.get(5))) {
                    car.setWeek(list.get(5));
                    carFile.setWeek(list.get(5));
                } else {
                    flag = true;
                    errorInfo += "星期为空或者格式不正确-";
                }
                //校验日期
                if (StringUtil.isNotBlank(list.get(6))) {
                    //校验日期
                    car.setDate(list.get(6));
                    carFile.setDate(list.get(6));
                    //校验车牌号和日期组合
                    int i1 = carMapper.selectCountByLicensePlateNumberAndDate(car.getLicensePlateNumber(), list.get(6));
                    if (i1 > 0) {
                        flag = true;
                        errorInfo += "数据库中已存在车牌号和日期组合的数据-";
                    }
                } else {
                    flag = true;
                    errorInfo += "日期为空-";
                }
                //校验配送域
                if (StringUtil.isNotBlank(list.get(7))) {
                    Delivery delivery = deliveryMapper.selectByName(list.get(7));
                    if (delivery == null) {
                        flag = true;
                        errorInfo += "配送域不存在-";
                    } else {
                        carFile.setDeliveryAreaName(list.get(7));
                        car.setDeliveryAreaId(delivery.getDeliveryAreaId());
                        //设置中转站
                        car.setTransitDepotId((long) delivery.getTransitDepotId());
                    }
                } else {
                    flag = true;
                    errorInfo += "配送域为空-";
                }

                //收集全部数据
                CarFile carFileAll = new CarFile();
                carFileAll.setLicensePlateNumber(list.get(0));
                carFileAll.setCarDriverName(list.get(1));
                carFileAll.setActualLoad(list.get(2));
                carFileAll.setActualTime(list.get(3));
                carFileAll.setRouteName(list.get(4));
                carFileAll.setWeek(list.get(5));
                carFileAll.setDate(list.get(6));
                carFileAll.setDeliveryAreaName(list.get(7));


                if (flag) {
                    list.add("失败");
                    if (errorInfo.length() > 0) {
                        errorInfo = errorInfo.substring(0, errorInfo.length() - 1);
                    }
                    list.add(errorInfo);
                    errorList.add(list);


                    carFileAll.setConclusion(list.get(8));
                    carFileAll.setImportDetails(list.get(9));

                    allCarList.add(carFileAll);
                    continue;
                }
                //收集成功结果
                //插入到数据库
                //导入完成后
                car.setIsDelete(0);
                car.setStatus("1");
                car.setIs_fact("1");
                int insert = carMapper.insert(car);
                if (insert == 1) {
                    carFile.setConclusion("成功");
                    list.add("成功");
                    carFiles.add(carFile);
                    succeedList.add(list);
                } else {
                    carFile.setConclusion("失败");
                    carFile.setImportDetails("插入数据库时失败");
                    list.add("失败");
                    carFileAll.setImportDetails(list.get(9));
                }
                cars.add(car);
                carFileAll.setConclusion(list.get(8));
                allCarList.add(carFileAll);
            }
            /*for (Car car : cars) {
                carMapper.insert(car);
            }*/

            String status = "全部导入成功";
            if (errorList.size() > 0 && carFiles.size() > 0) {
                status = "部分导入成功";
                allCarList.get(0).setImportDetails(String.format("共%d条记录，导入成功%d条，导入失败%d条", allCarList.size() - 1, carFiles.size(), errorList.size()));
            } else if (carFiles.size() == 0) {
                status = "全部导入失败";
                allCarList.get(0).setImportDetails("全部导入失败");
            } else {
                status = "全部导入成功";
                allCarList.get(0).setImportDetails("全部导入成功");
            }
            //导出全部数据
            if (allCarList.size() > 1) {
                //导出路径-docker
                String[] split = formattedDate.split(" ");
                String datePart = split[0];// 2024/10/8
                String[] split1 = datePart.split("/");
                String s = String.valueOf(Integer.parseInt(split1[1]));
                String s1 = String.valueOf(Integer.parseInt(split1[2]));
                datePart = split1[0] + "/" + s + "/" + s1;
                String timePart = split[1];
                String[] timeSplit = timePart.split(":");
                String path = DOWNLOAD_PATH + datePart + "/";
                path += timeSplit[0] + "/" + timeSplit[1] + "/" + timeSplit[2] + "/";
                System.out.println(path);
                //CsvUtils.writeCSVFile("/www/wwwroot/ycwl/ycwlms/data-management/data/"+csvFile.getName(),allCarList);

                File directory = new File(path);
                // 检查目录是否存在
                if (directory.exists()) {
                    System.out.println("Directory already exists.");
                } else {
                    // 尝试创建目录
                    boolean isDirectoryCreated = directory.mkdirs();
                    if (isDirectoryCreated) {
                        System.out.println("Directory created successfully.");
                    } else {
                        System.out.println("Failed to create directory.");
                    }
                }
                CsvUtils.writeCSVFile(path + csvFile.getName(), allCarList, 1);
            }
            // 将结果插入数据库中
            String userName = JWT.decode(authorization).getClaim("userName").asString();

            FileImportLogs fileImportLogs = new FileImportLogs();
            fileImportLogs.setFileName(csvFile.getName());
            fileImportLogs.setImportTime(formattedDate);
            fileImportLogs.setUserName(userName);
            fileImportLogs.setStatus(status);
            fileImportLogs.setFileSize(formatFileSize(csvFile));
            fileImportLogs.setStoreOrCar("1");
            fileImportLogsMapper.insert(fileImportLogs);
        } else {
            return "导入失败，数据为空";
        }
        return "导入成功";
    }


    //返回文件大小
    public static String formatFileSize(File file) {
        if (!file.exists() || !file.isFile()) {
            return "File does not exist or is not a regular file.";
        }

        long fileSizeInBytes = file.length();
        double fileSizeInKB = fileSizeInBytes / 1024.0;
        double fileSizeInMB = fileSizeInKB / 1024.0;

        if (fileSizeInBytes < 1024) {
            return fileSizeInBytes + " bytes";
        } else if (fileSizeInKB >= 1 && fileSizeInMB < 1) {
            return String.format("%.2f KB", fileSizeInKB);
        } else if (fileSizeInMB >= 1) {
            return String.format("%.2f MB", fileSizeInMB);
        } else {
            return "File size is zero or unknown.";
        }
    }


    @Override
    public AddCarDownBoxVO getCarDownBox() {
        AddCarDownBoxVO addCarDownBoxVO = new AddCarDownBoxVO();

        //处理配送域
        List<String> deliverys = deliveryMapper.selectNameList();
        addCarDownBoxVO.setDeliveryList(deliverys);
        //处理驾驶人
        List<CarDriver> carDrivers = carMapper.selectCarDriver();
        addCarDownBoxVO.setCarDriver(carDrivers);
        //处理状态
        ArrayList<String> statusList = new ArrayList<>();
        statusList.add("正常");
        statusList.add("异常");
        addCarDownBoxVO.setStatus(statusList);
        return addCarDownBoxVO;
    }

    public static String detectEncoding(File file) throws IOException {
        BufferedInputStream input = new BufferedInputStream(new FileInputStream(file));
        BOMInputStream bomInput = new BOMInputStream(input);
        UniversalDetector detector = new UniversalDetector(null);

        int nread;
        byte[] buf = new byte[4096];
        while ((nread = bomInput.read(buf)) > 0 && !detector.isDone()) {
            detector.handleData(buf, 0, nread);
        }
        detector.dataEnd();

        String encoding = detector.getDetectedCharset();
        detector.reset();
        bomInput.close();

        return encoding;
    }

    @Override
    @Transactional
    public String exportExcel(String formattedDate, File file, String authorization) {
        String fileName = file.getPath();
        System.out.println("fileName: " + fileName);
        // 这里默认每次会读取100条数据 然后返回过来 直接调用使用数据就行
        // 具体需要返回多少行可以在`PageReadListener`的构造函数设置
        //收集成功的结果
        ArrayList<CarExcelFile> carExcelFiles = new ArrayList<>();
        //收集全部结果
        ArrayList<CarExcelFile> allCarExcelFiles = new ArrayList<>();
        allCarExcelFiles.add(new CarExcelFile());
        ArrayList<String> weekList = new ArrayList<>();
        weekList.add("星期一");
        weekList.add("星期二");
        weekList.add("星期三");
        weekList.add("星期四");
        weekList.add("星期五");
        weekList.add("星期六");
        weekList.add("星期日");


        String regex = "^(\\d{4})/(0[1-9]|[1-9]|1[0-2])/([1-9]|0[1-9]|[12]\\d|3[01])$";

        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);

        EasyExcel.read(fileName, CarExcelFile.class, new PageReadListener<CarExcelFile>(dataList -> {
            for (CarExcelFile excel : dataList) {
                CarExcelFile carExcelFile = new CarExcelFile();
                ArrayList<String> errorInfo = new ArrayList<>();

                //处理车牌号
                String licensePlateNumber = excel.getLicensePlateNumber();
                if (!StringUtil.isNotBlank(licensePlateNumber)) {
                    errorInfo.add("车牌号为空");
                } else {
                    Car car = carMapper.selectCarByLicensePlateNumber(licensePlateNumber);
                    if (car == null) {
                        errorInfo.add("车牌号不存在");
                    } else {
                        carExcelFile.setLicensePlateNumber(licensePlateNumber);
                    }
                }
                //处理联系人
                String carDriverName = excel.getCarDriverName();
                if (!StringUtil.isNotBlank(carDriverName)) {
                    errorInfo.add("驾驶人为空");
                } else {
                    //查询驾驶人是否存在
                    int i = carMapper.selectCarDriverNumber(carDriverName);
                    if (i <= 0) {
                        errorInfo.add("驾驶人不存在");
                    } else {
                        carExcelFile.setCarDriverName(carDriverName);
                    }

                }
                //处理实际载货量
                String actualLoad = excel.getActualLoad();
                if (!StringUtil.isNotBlank(actualLoad)) {
                    errorInfo.add("实际载货量为空");
                } else {
                    carExcelFile.setActualLoad(actualLoad);
                }
                //处理实际工作时长
                String actualTime = excel.getActualTime();
                if (!StringUtil.isNotBlank(actualTime)) {
                    errorInfo.add("实际工作时长为空");
                } else {
                    carExcelFile.setActualTime(actualTime);
                }
                //处理路线
                String routeName = excel.getRouteName();
                if (!StringUtil.isNotBlank(routeName)) {
                    errorInfo.add("路线为空");
                } else {
                    int i1 = carMapper.selectCountByLicensePlateNumberAndRoute(licensePlateNumber, routeName);
                    if (i1 > 0) {
                        errorInfo.add("数据库中已存在车牌号和路线组合的数据");
                    } else {
                        carExcelFile.setRouteName(routeName);
                    }
                }
                //处理星期
                //处理路线
                String week = excel.getWeek();
                if (!StringUtil.isNotBlank(week)) {
                    errorInfo.add("星期为空");
                } else {
                    if (!weekList.contains(week)) {
                        errorInfo.add("星期格式错误");
                    } else {
                        carExcelFile.setWeek(week);
                    }
                }
                //处理日期
                //处理路线
                String date = excel.getDate();
                if (!StringUtil.isNotBlank(date)) {
                    errorInfo.add("日期为空");
                } else {
                    Matcher matcher = pattern.matcher(date);
                    if (!matcher.matches()) {
                        errorInfo.add("日期格式不正确");
                    } else {
                        int i1 = carMapper.selectCountByLicensePlateNumberAndDate(licensePlateNumber, date);
                        if (i1 > 0) {
                            errorInfo.add("数据库中已存在车牌号和日期组合的数据");
                        }
                        carExcelFile.setDate(date);
                    }
                }
                //处理配送域
                String deliveryAreaName = excel.getDeliveryAreaName();
                if (!StringUtil.isNotBlank(deliveryAreaName)) {
                    errorInfo.add("配送域为空");
                } else {
                    Delivery delivery = deliveryMapper.selectByName(deliveryAreaName);
                    if (delivery == null) {
                        errorInfo.add("配送域不存在");
                    } else {
                        carExcelFile.setDeliveryAreaName(deliveryAreaName);
                    }
                }
                //设置状态
                String errors = "";
                if (errorInfo.size() != 0) {
                    //设置详细信息
                    excel.setConclusion("失败");
                    errors = String.join("-", errorInfo);
                } else {
                    //将成功数据插入到数据库中
                    Car car = new Car();
                    car.setLicensePlateNumber(carExcelFile.getLicensePlateNumber());
                    Long aLong = carMapper.selectCarDriverIdByName(carExcelFile.getCarDriverName());//添加送货员时不能重名
                    car.setCarDriverId(aLong);
                    car.setActualLoad(carExcelFile.getActualLoad());
                    car.setActualTime(carExcelFile.getActualTime());
                    car.setRouteName(carExcelFile.getRouteName());
                    car.setWeek(carExcelFile.getWeek());
                    car.setDate(carExcelFile.getDate());
                    car.setStatus("1");
                    car.setIs_fact("1");
                    //处理配送域
                    Delivery delivery = deliveryMapper.selectByName(carExcelFile.getDeliveryAreaName());
                    car.setDeliveryAreaId(delivery.getDeliveryAreaId());
                    //设置中转站
                    car.setTransitDepotId((long) delivery.getTransitDepotId());
                    //将成功数据插入到数据库中
                    int insert = carMapper.insert(car);
                    if (insert == 1) {
                        carExcelFile.setConclusion("成功");
                        excel.setConclusion("成功");
                        carExcelFiles.add(carExcelFile);
                    } else {
                        carExcelFile.setConclusion("失败");
                        excel.setConclusion("失败");
                        errorInfo.add("添加到数据库时失败");
                        errors = String.join("-", errorInfo);
                    }
                }
                excel.setImportDetails(errors);
                allCarExcelFiles.add(excel);
            }
        })).sheet().doRead();

        //将导入日志导入数据库
        FileImportLogs fileImportLogs = new FileImportLogs();
        fileImportLogs.setFileName(file.getName());
        fileImportLogs.setImportTime(formattedDate);
        String status = "";
        if (carExcelFiles.size() != 0 && allCarExcelFiles.size() - 1 > carExcelFiles.size()) {
            status = "部分导入成功";
            allCarExcelFiles.get(0).setImportDetails(String.format("共%d条记录，导入成功%d条，导入失败%d条", allCarExcelFiles.size() - 1, carExcelFiles.size(), allCarExcelFiles.size() - carExcelFiles.size() - 1));
        } else if (carExcelFiles.size() == 0) {
            status = "全部导入失败";
            allCarExcelFiles.get(0).setImportDetails("全部导入失败");
        } else if (carExcelFiles.size() == allCarExcelFiles.size() - 1) {
            status = "全部导入成功";
            allCarExcelFiles.get(0).setImportDetails("全部导入成功");
        }
        String userName = JWT.decode(authorization).getClaim("userName").asString();
        fileImportLogs.setStatus(status);
        fileImportLogs.setUserName(userName);
        fileImportLogs.setFileSize(formatFileSize(file));
        fileImportLogs.setStoreOrCar("1");
        fileImportLogsMapper.insert(fileImportLogs);

        //处理日期
        String[] split = formattedDate.split(" ");
        String datePart = split[0];// 2024/10/8
        String[] split1 = datePart.split("/");
        String s = String.valueOf(Integer.parseInt(split1[1]));
        String s1 = String.valueOf(Integer.parseInt(split1[2]));
        datePart = split1[0] + "/" + s + "/" + s1;
        String timePart = split[1];
        String[] timeSplit = timePart.split(":");
        //String path = "/www/wwwroot/ycwl/ycwlms/data-management/data/Download/" + datePart + "/";
        String path = DOWNLOAD_PATH + datePart + "/";
        path += timeSplit[0] + "/" + timeSplit[1] + "/" + timeSplit[2] + "/";

        // 设置文件导出的路径
        File folder = new File(path);
        if (!folder.isDirectory()) {
            folder.mkdirs();
        }
        String fileName1 = path + file.getName();
        // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为用户表 然后文件流会自动关闭
        EasyExcel.write(fileName1, CarExcelFile.class).sheet("sheet").doWrite(allCarExcelFiles);

        System.out.println("dui: " + carExcelFiles);
        //如果数据有对有错则将全部数据表格导出数据库
        System.out.println("all: " + allCarExcelFiles);
        return "导入成功";
    }


    @Override
    public int deleteCarActual(Long carId) {
        return carMapper.deleteById(carId);
    }

    @Override
    public boolean checkExportFrom(File file, String filetype) {
        //判断文件类型
        if ("text/csv".equals(filetype)) {
            try (BufferedReader br = new BufferedReader(new FileReader(file))) {
                String s = br.readLine();
                //校验是否成功
                String[] split = s.split(",");
                String s1 = "";
                char[] chars = split[0].toCharArray();
                s1 += chars[1] + String.valueOf(chars[2]) + chars[3];
                String s3 ="";
                if(chars.length==5) {
                     s3 = chars[1] + String.valueOf(chars[2]) + chars[3] + chars[4];
                }
                if ("车牌号".equals(s1)) {
                    if(split.length!=8){
                        return false;
                    }
                    if (!"驾驶人名称".equals(split[1]) || !"实际载货量(吨)".equals(split[2]) || !"实际工作时长(小时)".equals(split[3])
                            || !"路线".equals(split[4]) || !"星期".equals(split[5]) || !"日期".equals(split[6]) || !"配送域".equals(split[7])) {
                        return false;
                    }
                } else if ("客户编码".equals(s3)) {
                    if(split.length!=11){
                        return false;
                    }
                    if (!"客户名称".equals(split[1]) || !"客户地址".equals(split[2]) || !"负责人".equals(split[3]) || !"客户档位".equals(split[4]) || !"GIS经度".equals(split[5])
                            || !"GIS维度".equals(split[6]) || !"线路".equals(split[7]) || !"访销周期".equals(split[8]) || !"客户专员".equals(split[9]) || !"配送域".equals(split[10])) {
                        return false;
                    }
                } else {
                    return false;
                }

                return true;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(filetype) || "application/vnd.ms-excel".equals(filetype)) {
            try {
                System.out.println("DEBUG: 开始读取Excel文件头部");
                String[] split = readHeaderRowFromFile(file);
                if (split == null) {
                    System.out.println("DEBUG: 读取Excel头部失败，split为null");
                    return false;
                }
                System.out.println("DEBUG: 读取到的头部数组长度: " + split.length);
                for (int i = 0; i < split.length; i++) {
                    System.out.println("DEBUG: 头部[" + i + "] = '" + split[i] + "'");
                }

                if ("车牌号".equals(split[0])) {
                    System.out.println("DEBUG: 识别为车辆实情表格");
                    if(split.length!=8){
                        System.out.println("DEBUG: 列数不正确，期望8列，实际" + split.length + "列");
                        return false;
                    }
                    if (!"驾驶人名称".equals(split[1]) || !"实际载货量(吨)".equals(split[2]) || !"实际工作时长(小时)".equals(split[3])
                            || !"路线".equals(split[4]) || !"星期".equals(split[5]) || !"日期".equals(split[6]) || !"配送域".equals(split[7])) {
                        System.out.println("DEBUG: 表头格式验证失败");
                        System.out.println("DEBUG: 期望格式: [车牌号,驾驶人名称,实际载货量(吨),实际工作时长(小时),路线,星期,日期,配送域]");
                        return false;
                    }
                    System.out.println("DEBUG: 车辆实情表格格式验证通过");
                } else if ("客户编码".equals(split[0])) {
                    if(split.length!=11){
                        return false;
                    }
                    if (!"客户名称".equals(split[1]) || !"客户地址".equals(split[2]) || !"负责人".equals(split[3]) || !"客户档位".equals(split[4]) || !"GIS经度".equals(split[5])
                            || !"GIS维度".equals(split[6]) || !"线路".equals(split[7]) || !"访销周期".equals(split[8]) || !"客户专员".equals(split[9]) || !"配送域".equals(split[10])) {
                        return false;
                    }
                    int i=0;

                } else {
                    return false;
                }
                return true;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            return false;
        }
    }

    public static String[] readHeaderRowFromFile(File excelFile) throws IOException {
        System.out.println("DEBUG: 开始读取Excel文件: " + excelFile.getName());
        FileInputStream fileInputStream = null;
        Workbook workbook = null;

        try {
            fileInputStream = new FileInputStream(excelFile);

            // 根据文件扩展名判断是xls还是xlsx文件
            if (excelFile.getName().endsWith(".xls")) {
                System.out.println("DEBUG: 识别为.xls文件");
                workbook = new HSSFWorkbook(fileInputStream);
            } else if (excelFile.getName().endsWith(".xlsx")) {
                System.out.println("DEBUG: 识别为.xlsx文件");
                workbook = new XSSFWorkbook(fileInputStream);
            } else {
                throw new IllegalArgumentException("The file is not an Excel file (.xls or .xlsx)");
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                System.out.println("DEBUG: 无法获取第一个工作表");
                return null;
            }

            // 获取第一行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                System.out.println("DEBUG: 无法获取第一行数据");
                return null;
            }

            // 读取表头数据
            int cellCount = headerRow.getLastCellNum();
            System.out.println("DEBUG: 表头列数: " + cellCount);
            String[] headerData = new String[cellCount];
            for (int i = 0; i < cellCount; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    headerData[i] = cell.toString().trim(); // 添加trim()去除空格
                    System.out.println("DEBUG: 读取单元格[" + i + "] = '" + headerData[i] + "'");
                }
            }

            System.out.println("DEBUG: Excel文件读取完成");
            return headerData;

        } finally {
            // 确保资源被正确关闭
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    System.out.println("DEBUG: 关闭workbook时出错: " + e.getMessage());
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    System.out.println("DEBUG: 关闭fileInputStream时出错: " + e.getMessage());
                }
            }
        }
    }
}
