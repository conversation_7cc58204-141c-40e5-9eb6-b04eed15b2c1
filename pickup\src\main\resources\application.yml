server:
  port: 8087
spring:
  application:
    name: pickup
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
mybatis:
  type-aliases-package: com.ict.ycwl.pickup.pojo
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.ycwl.pickup.pojo
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
file:
  DOWNLOAD_PATH: E:\www\wwwroot\ycwl\ycwl-ms\pickup\data\Download\
  UPLOAD_PATH: E:\www\wwwroot\ycwl\ycwl-ms\pickup\data\uploadFile\
  DOWNLOAD_NULL_FROM_PATH: E:\www\wwwroot\ycwl\ycwl-ms\pickup\data\nullFrom\